Looking in indexes: http://mirrors.aliyun.com/pypi/simple
Requirement already satisfied: numpy in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (1.21.6)
Collecting pandas
  Downloading http://mirrors.aliyun.com/pypi/packages/3e/0c/23764c4635dcb0a784a787498d56847b90ebf974e65f4ab4053a5d97b1a5/pandas-1.3.5-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.3 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 11.3/11.3 MB 50.3 MB/s eta 0:00:00
Collecting scikit-learn
  Downloading http://mirrors.aliyun.com/pypi/packages/bd/05/e561bc99a615b5c099c7a9355409e5e57c525a108f1c2e156abb005b90a6/scikit_learn-1.0.2-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (24.8 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 24.8/24.8 MB 24.6 MB/s eta 0:00:00
Collecting wandb
  Downloading http://mirrors.aliyun.com/pypi/packages/a3/65/8af6447adb236c0b487ae44f370cb24c8afda678b5acf7f1cb4469739048/wandb-0.18.7-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.1 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.1/16.1 MB 20.0 MB/s eta 0:00:00
Collecting entmax
  Downloading http://mirrors.aliyun.com/pypi/packages/06/a0/71747f0d98e441d0670b06205afd24d832e88c0ee62129ca47ce88505304/entmax-1.3-py3-none-any.whl (13 kB)
Collecting pytz>=2017.3
  Downloading http://mirrors.aliyun.com/pypi/packages/81/c4/34e93fe5f5429d7570ec1fa436f1986fb1f00c3e0f43a589fe2bbcd22c3f/pytz-2025.2-py2.py3-none-any.whl (509 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 509.2/509.2 kB 85.2 MB/s eta 0:00:00
Collecting python-dateutil>=2.7.3
  Downloading http://mirrors.aliyun.com/pypi/packages/ec/57/56b9bcc3c9c6a792fcbaf139543cee77261f3651ca9da0c93f5c1221264b/python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 229.9/229.9 kB 55.8 MB/s eta 0:00:00
Collecting threadpoolctl>=2.0.0
  Downloading http://mirrors.aliyun.com/pypi/packages/61/cf/6e354304bcb9c6413c4e02a747b600061c21d38ba51e7e544ac7bc66aecc/threadpoolctl-3.1.0-py3-none-any.whl (14 kB)
Collecting joblib>=0.11
  Downloading http://mirrors.aliyun.com/pypi/packages/10/40/d551139c85db202f1f384ba8bcf96aca2f329440a844f924c8a0040b6d02/joblib-1.3.2-py3-none-any.whl (302 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 302.2/302.2 kB 81.9 MB/s eta 0:00:00
Collecting scipy>=1.1.0
  Downloading http://mirrors.aliyun.com/pypi/packages/58/4f/11f34cfc57ead25752a7992b069c36f5d18421958ebd6466ecd849aeaf86/scipy-1.7.3-cp37-cp37m-manylinux_2_12_x86_64.manylinux2010_x86_64.whl (38.1 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 38.1/38.1 MB 37.9 MB/s eta 0:00:00
Collecting psutil>=5.0.0
  Downloading http://mirrors.aliyun.com/pypi/packages/bf/b9/b0eb3f3cbcb734d930fdf839431606844a825b23eaf9a6ab371edac8162c/psutil-7.0.0-cp36-abi3-manylinux_2_12_x86_64.manylinux2010_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (277 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 278.0/278.0 kB 93.3 MB/s eta 0:00:00
Collecting gitpython!=3.1.29,>=1.0.0
  Downloading http://mirrors.aliyun.com/pypi/packages/1d/9a/4114a9057db2f1462d5c8f8390ab7383925fe1ac012eaa42402ad65c2963/GitPython-3.1.44-py3-none-any.whl (207 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 207.6/207.6 kB 50.2 MB/s eta 0:00:00
Requirement already satisfied: requests<3,>=2.0.0 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from wandb) (2.28.1)
Requirement already satisfied: setuptools in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from wandb) (65.6.3)
Requirement already satisfied: typing-extensions<5,>=4.4 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from wandb) (4.4.0)
Collecting sentry-sdk>=2.0.0
  Downloading http://mirrors.aliyun.com/pypi/packages/f0/e5/da07b0bd832cefd52d16f2b9bbbe31624d57552602c06631686b93ccb1bd/sentry_sdk-2.29.1-py2.py3-none-any.whl (341 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 341.6/341.6 kB 87.4 MB/s eta 0:00:00
Collecting platformdirs
  Downloading http://mirrors.aliyun.com/pypi/packages/31/16/70be3b725073035aa5fc3229321d06e22e73e3e09f6af78dcfdf16c7636c/platformdirs-4.0.0-py3-none-any.whl (17 kB)
Collecting pyyaml
  Downloading http://mirrors.aliyun.com/pypi/packages/d7/8f/db62b0df635b9008fe90aa68424e99cee05e68b398740c8a666a98455589/PyYAML-6.0.1-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (670 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 670.1/670.1 kB 69.7 MB/s eta 0:00:00
Collecting protobuf!=4.21.0,!=5.28.0,<6,>=3.12.0
  Downloading http://mirrors.aliyun.com/pypi/packages/c8/2c/03046cac73f46bfe98fc846ef629cf4f84c2f59258216aa2cc0d22bfca8f/protobuf-4.24.4-cp37-abi3-manylinux2014_x86_64.whl (311 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 311.6/311.6 kB 85.4 MB/s eta 0:00:00
Collecting setproctitle
  Downloading http://mirrors.aliyun.com/pypi/packages/66/bf/620ee20caa0c5c1592af7c72dd3339714e2e7b50f97a47e53e3e65b56d6c/setproctitle-1.3.3-cp37-cp37m-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (30 kB)
Collecting click!=8.0.0,>=7.1
  Downloading http://mirrors.aliyun.com/pypi/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl (98 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 98.2/98.2 kB 57.1 MB/s eta 0:00:00
Collecting docker-pycreds>=0.4.0
  Downloading http://mirrors.aliyun.com/pypi/packages/f5/e8/f6bd1eee09314e7e6dee49cbe2c5e22314ccdb38db16c9fc72d2fa80d054/docker_pycreds-0.4.0-py2.py3-none-any.whl (9.0 kB)
Requirement already satisfied: torch>=1.3 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from entmax) (1.13.1+cpu)
Collecting importlib-metadata
  Downloading http://mirrors.aliyun.com/pypi/packages/ff/94/64287b38c7de4c90683630338cf28f129decbba0a44f0c6db35a873c73c4/importlib_metadata-6.7.0-py3-none-any.whl (22 kB)
Collecting six>=1.4.0
  Downloading http://mirrors.aliyun.com/pypi/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl (11 kB)
Collecting gitdb<5,>=4.0.1
  Downloading http://mirrors.aliyun.com/pypi/packages/a0/61/5c78b91c3143ed5c14207f463aecfc8f9dbb5092fb2869baf37c273b2705/gitdb-4.0.12-py3-none-any.whl (62 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 62.8/62.8 kB 39.6 MB/s eta 0:00:00
Requirement already satisfied: charset-normalizer<3,>=2 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from requests<3,>=2.0.0->wandb) (2.1.1)
Requirement already satisfied: idna<4,>=2.5 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from requests<3,>=2.0.0->wandb) (3.4)
Requirement already satisfied: certifi>=2017.4.17 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from requests<3,>=2.0.0->wandb) (2022.12.7)
Requirement already satisfied: urllib3<1.27,>=1.21.1 in /root/miniconda3/envs/pykt/lib/python3.7/site-packages (from requests<3,>=2.0.0->wandb) (1.26.13)
Collecting typing-extensions<5,>=4.4
  Downloading http://mirrors.aliyun.com/pypi/packages/ec/6b/63cc3df74987c36fe26157ee12e09e8f9db4de771e0f3404263117e75b95/typing_extensions-4.7.1-py3-none-any.whl (33 kB)
Collecting smmap<6,>=3.0.1
  Downloading http://mirrors.aliyun.com/pypi/packages/04/be/d09147ad1ec7934636ad912901c5fd7667e1c858e19d355237db0d0cd5e4/smmap-5.0.2-py3-none-any.whl (24 kB)
Collecting zipp>=0.5
  Downloading http://mirrors.aliyun.com/pypi/packages/5b/fa/c9e82bbe1af6266adf08afb563905eb87cab83fde00a0a08963510621047/zipp-3.15.0-py3-none-any.whl (6.8 kB)
Installing collected packages: pytz, zipp, typing-extensions, threadpoolctl, smmap, six, setproctitle, sentry-sdk, scipy, pyyaml, psutil, protobuf, joblib, scikit-learn, python-dateutil, platformdirs, importlib-metadata, gitdb, docker-pycreds, pandas, gitpython, entmax, click, wandb
  Attempting uninstall: typing-extensions
    Found existing installation: typing_extensions 4.4.0
    Uninstalling typing_extensions-4.4.0:
      Successfully uninstalled typing_extensions-4.4.0
Successfully installed click-8.1.8 docker-pycreds-0.4.0 entmax-1.3 gitdb-4.0.12 gitpython-3.1.44 importlib-metadata-6.7.0 joblib-1.3.2 pandas-1.3.5 platformdirs-4.0.0 protobuf-4.24.4 psutil-7.0.0 python-dateutil-2.9.0.post0 pytz-2025.2 pyyaml-6.0.1 scikit-learn-1.0.2 scipy-1.7.3 sentry-sdk-2.29.1 setproctitle-1.3.3 six-1.17.0 smmap-5.0.2 threadpoolctl-3.1.0 typing-extensions-4.7.1 wandb-0.18.7 zipp-3.15.0
