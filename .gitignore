examples/__pycache__/
examples/all_bestmodel/
examples/all_start.sh
examples/pkls/
examples/pred_wandbs/
examples/saved_model/
examples/start_predict.sh
examples/wandb/
dist/
*.egg-info/
*.pyc
*.pkl
saved_model
build/
.vscode/
data/assist*
data/assist2012
data/algebra2005
data/bridge2algebra2006
data/statics2011
data/nips_task34
data/assist2017
data/poj
data/slepemapy
data/junyi2015
data/ednet
.ipynb_checkpoints
dev_tools
tmp*
configs/wandb.json
examples/nohup.out
*.log
start_sweep_*.sh
wandb/*
*tiaocan*
examples/assist2009_dkt_que_mt_iekt
examples/assist2009_*
examples/aaai_dkt_improve_v1
tabchen
examples/aaai2023/
examples/all_wandbs/
data_old
examples/nips2022/
examples/results/wandb_result/
examples/*.pred
examples/nips_task34/
__pycache__/
log.*
*.swp
*.bak
logs/
examples/best_model_path
examples/all_wandbs_pred/
examples/best_model_path_1002
examples/pred_wandbs_all_best_models_lqq
examples/wandb_tmp/
examples/archive/
prediction.csv
data/peiyou
examples/iekt_ab/
examples/qikt_improve/
examples/*.txt
examples/nips2022-peiyou/
examples/nips2022-ednet/
examples/models/