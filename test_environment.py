#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试pyKT环境是否正确配置
"""

import sys
import os

print("=== pyKT环境测试 ===")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

# 测试基础包
try:
    import numpy as np
    print(f"✓ NumPy版本: {np.__version__}")
except ImportError as e:
    print(f"✗ NumPy导入失败: {e}")

try:
    import pandas as pd
    print(f"✓ Pandas版本: {pd.__version__}")
except ImportError as e:
    print(f"✗ Pandas导入失败: {e}")

try:
    import torch
    print(f"✓ PyTorch版本: {torch.__version__}")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
except ImportError as e:
    print(f"✗ PyTorch导入失败: {e}")

try:
    import sklearn
    print(f"✓ Scikit-learn版本: {sklearn.__version__}")
except ImportError as e:
    print(f"✗ Scikit-learn导入失败: {e}")

# 测试pyKT包
try:
    sys.path.insert(0, '.')
    import pykt
    print("✓ pyKT包导入成功")
    
    # 测试具体模块
    from pykt.models import init_model
    print("✓ pyKT模型模块导入成功")
    
    from pykt.datasets import init_dataset4train
    print("✓ pyKT数据集模块导入成功")
    
    from pykt.utils import set_seed
    print("✓ pyKT工具模块导入成功")
    
except ImportError as e:
    print(f"✗ pyKT包导入失败: {e}")

print("\n=== 环境测试完成 ===")
