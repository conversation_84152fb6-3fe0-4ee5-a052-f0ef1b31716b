program: wandb_lpkt_train.py
method: bayes
metric:
    goal: maximize
    name: validauc
parameters:
    model_name:
        values: ["lpkt"]
    dataset_name:
        values: ["xes"]
    emb_type:
        values: ["qid"]
    save_dir:
        values: ["models/lpkt_tiaocan"]
    d_a:
        values: [16,64]
    d_e:
        values: [16,64]
    d_k:
        values: [16,64]
    gamma:
        values: [0.01,0.03,0.05,0.07,0.1]
    dropout:
        values: [0.05,0.1,0.3,0.5]
    learning_rate:
        values: [1e-3, 1e-4, 1e-5]
    seed:
        values: [42, 3407]
    fold:
        values: [0, 1, 2, 3, 4]
