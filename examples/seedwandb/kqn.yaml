program: wandb_kqn_train.py
method: bayes
metric:
    goal: maximize
    name: validauc
parameters:
    model_name:
        values: ["kqn"]
    dataset_name:
        values: ["xes"]
    emb_type:
        values: ["qid"]
    save_dir:
        values: ["models/kqn_tiaocan"]
    n_hidden:
        values: [64,256]
    n_rnn_hidden:
        values: [64,256]
    n_mlp_hidden:
        values: [64,256]
    n_rnn_layers:
        values: [1, 2]
    learning_rate:
        values: [1e-3, 1e-4, 1e-5]
    dropout:
        values: [0.05,0.1,0.3,0.5]
    seed:
        values: [42, 3407]
    fold:
        values: [0, 1, 2, 3, 4]
 
