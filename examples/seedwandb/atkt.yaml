program: wandb_atkt_train.py
method: bayes
metric:
    goal: maximize
    name: validauc
parameters:
    model_name:
        values: ["atkt"]
    dataset_name:
        values: ["xes"]
    emb_type:
        values: ["qid"]
    save_dir:
        values: ["models/atkt_tiaocan"]
    skill_dim:
        values: [64, 256]
    answer_dim:
        values: [64, 256]
    hidden_dim:
        values: [64, 256]
    attention_dim:
        values: [64, 256]
    epsilon:
        values: [1, 5, 10, 12, 15]
    beta:
        values: [0, 0.2, 0.5, 1, 2]
    dropout:
        values: [0.05,0.1,0.3,0.5]
    learning_rate:
        values: [1e-3, 1e-4, 1e-5]
    seed:
        values: [42, 3407]
    fold:
        values: [0, 1, 2, 3, 4]
