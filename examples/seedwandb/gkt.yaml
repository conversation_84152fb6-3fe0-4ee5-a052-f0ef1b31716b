program: wandb_gkt_train.py
method: grid
metric:
    goal: maximize
    name: validauc
parameters:
    model_name:
        values: ["gkt"]
    dataset_name:
        values: ["xes"]
    emb_type:
        values: ["qid"]
    save_dir:
        values: ["models/gkt_tiaocan"]
    hidden_dim:
        values: [16,64]
    dropout:
        values: [0.05,0.1,0.3,0.5]
    learning_rate:
        values: [1e-3, 1e-4, 1e-5]
    seed:
        values: [42, 3407]
    fold:
        values: [0, 1, 2, 3, 4]
