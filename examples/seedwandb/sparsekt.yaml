program: ./wandb_sparsekt_train.py
method: bayes
metric:
    goal: maximize
    name: validauc
parameters:
    model_name:
        values: ["sparsekt"]
    dataset_name:
        values: ["xes"]
    emb_type:
        values: ["qid"]
    save_dir:
        values: ["models/sparsekt_tiaocan"]
    d_model:
        values: [64, 256]
    d_ff:
        values: [64, 256]
    final_fc_dim:
        values: [64, 256]
    final_fc_dim2:
        values: [64, 256]
    dropout:
        values: [0.05,0.1,0.3,0.5]
    learning_rate:
        values: [1e-3, 1e-4, 1e-5]
    num_attn_heads:
        values: [4, 8]
    n_blocks:
        values: [1, 2, 4]
    sparse_ratio:
        values: [0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9]
    k_index:
        values: [1,2,3,4,5,6,7,8,9,10]
    seed:
        values: [42, 3407]
    fold:
        values: [0, 1, 2, 3, 4]
