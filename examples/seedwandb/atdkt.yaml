program: wandb_atdkt_train.py
method: bayes
metric:
    goal: maximize
    name: validauc
parameters:
    model_name:
        values: ["atdkt"]
    dataset_name:
        values: ["xes"]
    emb_type:
        values: ["qiddelxembhistranscembpredcurc"]
    save_dir:
        values: ["models/atdkt_tiaocan"]
    emb_size:
        values: [64, 256]
    num_attn_heads:
        values: [4, 8]
    num_layers:
        values: [1, 2, 4]
    l1:
        values: [1]
    l2:
        values: [0.01,0.1,0.3,0.5,0.7,1.0]
    l3:
        values: [0.01,0.1,0.3,0.5,0.7,1.0]
    start:
        values: [0, 10, 30, 50, 70, 100, 120, 150]
    learning_rate:
        values: [1e-3, 1e-4, 1e-5]
    dropout:
        values: [0.05,0.1,0.3,0.5]
    seed:
        values: [42, 3407]
    fold:
        values: [0, 1, 2, 3, 4]
