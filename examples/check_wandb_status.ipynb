{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c534551c-506f-4735-a641-e14e85ec5bea", "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload "]}, {"cell_type": "code", "execution_count": 2, "id": "b05f9109-0a9e-4dd3-a2aa-6d08b32675ef", "metadata": {}, "outputs": [], "source": ["from pykt.utils.wandb_utils import WandbUtils\n", "import pandas as pd\n", "import os\n", "from tqdm import tqdm_notebook\n", "import yaml\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "7af4c9e2-1359-4811-89e0-165de14ecac9", "metadata": {}, "outputs": [], "source": ["key = \"\"\n", "os.environ['WANDB_API_KEY'] = key\n", "wandb_api = WandbUtils(user='pykt-team', project_name=f'dkt')"]}, {"cell_type": "code", "execution_count": null, "id": "1dc685fa-c361-45d8-923a-03600e2b6a76", "metadata": {}, "outputs": [], "source": ["model_name=\"dkt\"\n", "dataset_name=\"assist2015\"\n", "check_result_list = wandb_api.check_sweep_by_model_dataset_name(dataset_name, model_name, emb_type=\"qid\")"]}, {"cell_type": "code", "execution_count": null, "id": "206a6b3e-62c2-42f3-8d48-a2fc9c76ed32", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}