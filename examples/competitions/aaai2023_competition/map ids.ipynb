{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["questions = json.load(open(\"questions.json\"))\n", "keyid2idx = json.load(open(\"keyid2idx.json\"))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["keyidx2id = {\"questions\":dict(zip(keyid2idx['questions'].values(),keyid2idx['questions'].keys())),\n", "             \"concepts\":dict(zip(keyid2idx['concepts'].values(),keyid2idx['concepts'].keys()))\n", "            }"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["question_id = 2203"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Get the origian question id."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'3395'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["origin_question_id = keyidx2id['questions'][question_id]\n", "origin_question_id"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Get question concetp info."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['121----169----1096----2258----1191']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["questions[origin_question_id]['concept_routes']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We use the last concept of concept routes as our concepts. In here is 1191.\n", "\n", "Then we map the origin concept id to the final id."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["140"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["keyid2idx['concepts']['1191']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.4"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}}, "nbformat": 4, "nbformat_minor": 2}