#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的pyKT环境测试
"""

import sys
import os
sys.path.insert(0, '.')

print("=== 简单环境测试 ===")

# 测试基础导入
try:
    import torch
    print(f"✓ PyTorch {torch.__version__} 导入成功")
    print(f"  设备: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
except Exception as e:
    print(f"✗ PyTorch导入失败: {e}")
    sys.exit(1)

try:
    import numpy as np
    import pandas as pd
    print(f"✓ NumPy {np.__version__} 和 Pandas {pd.__version__} 导入成功")
except Exception as e:
    print(f"✗ 基础包导入失败: {e}")
    sys.exit(1)

# 测试pyKT核心模块
try:
    from pykt.utils.utils import set_seed
    print("✓ pyKT utils模块导入成功")
except Exception as e:
    print(f"✗ pyKT utils模块导入失败: {e}")

try:
    from pykt.models.dkt import DKT
    print("✓ pyKT DKT模型导入成功")
except Exception as e:
    print(f"✗ pyKT DKT模型导入失败: {e}")

# 测试数据配置
try:
    import json
    with open('configs/data_config.json', 'r') as f:
        data_config = json.load(f)
    print("✓ 数据配置文件读取成功")
    print(f"  可用数据集: {list(data_config.keys())}")
except Exception as e:
    print(f"✗ 数据配置文件读取失败: {e}")

print("\n=== 测试完成 ===")
