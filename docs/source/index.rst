Welcome to pyKT's documentation!
========================================
pyKT is a python library build upon PyTorch to train deep learning based knowledge tracing (KT) models. The library consists of a standardized set of integrated data preprocessing procedures on multi popular datasets across different domains, 5 detailed prediction scenarios, frequently compared DLKT approaches for transparent and extensive experiments. 

Let's Get Started! `English Introduction <./quick_start.html>`_.

More details about the academic information can be read in our paper at https://arxiv.org/abs/2206.11460?context=cs.CY .


.. toctree::
   :maxdepth: 2
   :caption: Home
   
   Quick Start <quick_start>
   Models <models>
   Datasets <datasets>
   Contribute <contribute>

.. toctree::
   :maxdepth: 1
   :caption: API

   Models<pykt.models>
   Datasets<pykt.datasets>
   Data Preprocess<pykt.preprocess>
   Utils<pykt.utils>

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`


