{"assist2015": {"dpath": "../data/assist2015", "num_q": 0, "num_c": 100, "input_type": ["concepts"], "max_concepts": 1, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "algebra2005": {"dpath": "../data/algebra2005", "num_q": 173113, "num_c": 112, "input_type": ["questions", "concepts"], "max_concepts": 7, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "assist2009": {"dpath": "../data/assist2009", "num_q": 17737, "num_c": 123, "input_type": ["questions", "concepts"], "max_concepts": 4, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "slepemapy": {"dpath": "../data/slepemapy", "num_q": 2913, "num_c": 1458, "input_type": ["questions", "concepts"], "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv"}, "assist2017": {"dpath": "../data/assist2017", "num_q": 3162, "num_c": 102, "input_type": ["questions", "concepts"], "min_seq_len": 3, "maxlen": 500, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv"}, "assist2012": {"dpath": "../data/assist2012", "num_q": 53070, "num_c": 265, "input_type": ["questions", "concepts"], "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv"}, "statics2011": {"dpath": "../data/statics2011", "num_q": 0, "num_c": 1223, "input_type": ["concepts"], "max_concepts": 1, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "junyi2015": {"dpath": "../data/junyi2015", "num_q": 721, "num_c": 39, "input_type": ["questions", "concepts"], "max_concepts": 1, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv"}, "bridge2algebra2006": {"dpath": "../data/bridge2algebra2006", "num_q": 129263, "num_c": 493, "input_type": ["questions", "concepts"], "max_concepts": 5, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "ednet": {"dpath": "../data/ednet", "num_q": 11901, "num_c": 188, "input_type": ["questions", "concepts"], "max_concepts": 7, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "nips_task34": {"dpath": "../data/nips_task34", "num_q": 948, "num_c": 57, "input_type": ["questions", "concepts"], "max_concepts": 2, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "poj": {"dpath": "../data/poj", "num_q": 0, "num_c": 2748, "input_type": ["concepts"], "max_concepts": 1, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "peiyou": {"dpath": "../data/peiyou", "num_q": 7652, "num_c": 865, "input_type": ["questions", "concepts"], "max_concepts": 6, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "train_valid_original_file": "train_valid.csv", "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}, "ednet5w": {"dpath": "../data/ednet5w", "num_q": 12235, "num_c": 188, "input_type": ["questions", "concepts"], "max_concepts": 7, "min_seq_len": 3, "maxlen": 200, "emb_path": "", "train_valid_original_file": "train_valid.csv", "train_valid_file": "train_valid_sequences.csv", "folds": [0, 1, 2, 3, 4], "test_original_file": "test.csv", "test_file": "test_sequences.csv", "test_window_file": "test_window_sequences.csv", "test_question_file": "test_question_sequences.csv", "test_question_window_file": "test_question_window_sequences.csv", "train_valid_original_file_quelevel": "train_valid_quelevel.csv", "train_valid_file_quelevel": "train_valid_sequences_quelevel.csv", "test_file_quelevel": "test_sequences_quelevel.csv", "test_window_file_quelevel": "test_window_sequences_quelevel.csv", "test_original_file_quelevel": "test_quelevel.csv"}}